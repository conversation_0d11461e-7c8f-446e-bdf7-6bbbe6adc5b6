module.exports = {
  apps: [{
    name: 'crypto-assistant-backend',
    script: 'dist/index.js',
    cwd: '/www/wwwroot/crypto2.testingtools.site/backend',
    instances: 1,
    exec_mode: 'fork',
    env: {
      NODE_ENV: 'production',
      PORT: 5001
    },
    env_production: {
      NODE_ENV: 'production',
      PORT: 5001
    },
    error_file: '/www/wwwroot/crypto2.testingtools.site/backend/logs/error.log',
    out_file: '/www/wwwroot/crypto2.testingtools.site/backend/logs/out.log',
    log_file: '/www/wwwroot/crypto2.testingtools.site/backend/logs/combined.log',
    time: true,
    max_memory_restart: '1G',
    restart_delay: 4000,
    watch: false,
    ignore_watch: ['node_modules', 'logs', 'dist'],
    min_uptime: '10s',
    max_restarts: 10,
    autorestart: true,
    kill_timeout: 5000,
    listen_timeout: 8000,
    log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
    merge_logs: true,
    // Health check
    health_check_grace_period: 3000,
    // Environment variables
    env_file: '.env'
  }]
};
