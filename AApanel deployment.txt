First install Node js + node js manager + npm:

(These are seperate commands, run one by one)

sudo apt update

curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -

sudo apt-get install -y nodejs

# Verify installation

node --version

npm --version


Then next step is to clone the project repo: 

sudo git clone https://github.com/umerfarooqlaghari/Crypto_Assistant.git 

cd Crypto_Assistant

cd backend

sudo touch .env 

sudo nano .env 

Copy and paste these: 

# Production Environment for VPS
NODE_ENV=production
PORT=5000

# CORS Configuration - Include your VPS frontend URL
CORS_ORIGINS=http://localhost:3000,https://a1.testingtools.site,http://************:3000

# Database Configuration
DATABASE_URL="prisma+postgres://accelerate.prisma-data.net/?api_key=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqd3RfaWQiOjEsInNlY3VyZV9rZXkiOiJza19DQWNvamJrTjh1Z3hkYjVBbXdHVWYiLCJhcGlfa2V5IjoiMDFLMFc5OVcxQ0FTUDZHNU1LTk1WWFZNUEIiLCJ0ZW5hbnRfaWQiOiJmOGQ4ZDFmODI4N2MxYTQ1ODI1NzMyOTMxN2JiN2Y2MzM1MzdjYmNhMWNhMjZhZGEwYzVjZWFiNjJlY2QwZTM1IiwiaW50ZXJuYWxfc2VjcmV0IjoiOTZjYTg3ZDEtODg2OS00ZWU0LTgzMGYtNDNjNjcyMTNjODcxIn0.IInAiWKzuKwUEcBkjaG5oRhyf_p6fB0aLVSHa09xB8U"

# Security
JWT_SECRET=dev-jwt-secret-key-change-in-production
JWT_EXPIRE=24h

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=info

# WebSocket
WS_PORT=5002

# Signal Processing
SIGNAL_UPDATE_INTERVAL=60000
MAX_CANDLE_HISTORY=1000

# Alternative API Endpoints
ALTERNATIVE_API_URL=https://denke-umer.netlify.app



THEN: 

Press Controll O to save, press Enter, then press Control X to exit the file. 


Then: 

sudo npm install 

sudo npm run build



then: 

cd ..
cd frontend

sudo touch .env.local

sudo nano .env.local

PASTE THIS IN THERE: 




