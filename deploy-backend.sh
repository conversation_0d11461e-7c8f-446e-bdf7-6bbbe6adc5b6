#!/bin/bash

# Crypto Assistant Backend Deployment Script for AAPanel
# This script automates the backend deployment process

set -e  # Exit on any error

# Configuration
BACKEND_DIR="/www/wwwroot/crypto2.testingtools.site/backend"
APP_NAME="crypto-assistant-backend"
NODE_VERSION="18"

echo "🚀 Starting Crypto Assistant Backend Deployment..."

# Check if running as root or with sudo
if [[ $EUID -eq 0 ]]; then
   echo "⚠️  This script should not be run as root. Please run as www-data or regular user with sudo privileges."
   exit 1
fi

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
echo "📋 Checking prerequisites..."

if ! command_exists node; then
    echo "❌ Node.js not found. Please install Node.js $NODE_VERSION or higher."
    exit 1
fi

if ! command_exists npm; then
    echo "❌ npm not found. Please install npm."
    exit 1
fi

if ! command_exists pm2; then
    echo "❌ PM2 not found. Installing PM2..."
    sudo npm install -g pm2
fi

# Check Node.js version
NODE_CURRENT=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_CURRENT" -lt "$NODE_VERSION" ]; then
    echo "❌ Node.js version $NODE_CURRENT is too old. Please install Node.js $NODE_VERSION or higher."
    exit 1
fi

echo "✅ Prerequisites check passed"

# Create directory if it doesn't exist
echo "📁 Setting up directory structure..."
sudo mkdir -p "$BACKEND_DIR"
sudo mkdir -p "$BACKEND_DIR/logs"
sudo chown -R www-data:www-data "/www/wwwroot/crypto2.testingtools.site"

# Navigate to backend directory
cd "$BACKEND_DIR"

# Copy files if not already present
if [ ! -f "package.json" ]; then
    echo "📦 Copying backend files..."
    # Assuming the script is run from the project root
    cp -r ./backend/* "$BACKEND_DIR/"
    sudo chown -R www-data:www-data "$BACKEND_DIR"
fi

# Install dependencies
echo "📦 Installing dependencies..."
npm ci --production

# Build the application
echo "🔨 Building application..."
npm run build

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "⚙️  Creating environment file..."
    if [ -f ".env.production" ]; then
        cp .env.production .env
        echo "📝 Please edit .env file with your actual configuration values"
        echo "   Database URL, API keys, JWT secret, etc."
    else
        echo "❌ No .env.production template found. Please create .env file manually."
        exit 1
    fi
fi

# Setup database
echo "🗄️  Setting up database..."
if command_exists psql; then
    echo "PostgreSQL detected. Running Prisma migrations..."
    npx prisma generate
    npx prisma db push
else
    echo "⚠️  PostgreSQL not detected. Make sure your database is properly configured."
fi

# Stop existing PM2 process if running
echo "🔄 Managing PM2 processes..."
if pm2 list | grep -q "$APP_NAME"; then
    echo "Stopping existing $APP_NAME process..."
    pm2 stop "$APP_NAME"
    pm2 delete "$APP_NAME"
fi

# Start application with PM2
echo "🚀 Starting application with PM2..."
pm2 start ecosystem.config.js

# Save PM2 configuration
pm2 save

# Setup PM2 startup script
echo "⚙️  Setting up PM2 startup script..."
sudo env PATH=$PATH:/usr/bin /usr/lib/node_modules/pm2/bin/pm2 startup systemd -u www-data --hp /var/www

# Test the application
echo "🧪 Testing application..."
sleep 5

if pm2 list | grep -q "$APP_NAME.*online"; then
    echo "✅ Backend application is running successfully!"
    
    # Test API endpoint
    if command_exists curl; then
        echo "🔍 Testing API endpoint..."
        if curl -f -s "http://localhost:5001/api/health" > /dev/null; then
            echo "✅ API health check passed!"
        else
            echo "⚠️  API health check failed. Check logs with: pm2 logs $APP_NAME"
        fi
    fi
else
    echo "❌ Failed to start backend application. Check logs with: pm2 logs $APP_NAME"
    exit 1
fi

# Display status
echo ""
echo "🎉 Backend deployment completed successfully!"
echo ""
echo "📊 Application Status:"
pm2 list
echo ""
echo "📝 Useful commands:"
echo "   View logs: pm2 logs $APP_NAME"
echo "   Restart:   pm2 restart $APP_NAME"
echo "   Stop:      pm2 stop $APP_NAME"
echo "   Monitor:   pm2 monit"
echo ""
echo "🌐 Backend should be accessible at: http://localhost:5001"
echo "   Configure reverse proxy in AAPanel to: https://crypto2.testingtools.site"
echo ""
echo "⚠️  Don't forget to:"
echo "   1. Configure reverse proxy in AAPanel"
echo "   2. Set up SSL certificate"
echo "   3. Update CORS_ORIGINS in .env file"
echo "   4. Configure firewall rules"
echo ""
