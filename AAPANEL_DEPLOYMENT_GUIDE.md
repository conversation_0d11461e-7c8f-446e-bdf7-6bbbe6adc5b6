# AAPanel Deployment Guide for Crypto Assistant

This guide will help you deploy the Crypto Assistant application on AAPanel using two subdomains:
- **Frontend**: `crypto.testingtools.site` (Next.js application)
- **Backend**: `crypto2.testingtools.site` (Node.js API server)

## Prerequisites

1. AAPanel installed and configured
2. Node.js 18+ installed on the server
3. PM2 process manager installed
4. PostgreSQL database (or SQLite for testing)
5. SSL certificates for both domains

## Step 1: Server Preparation

### 1.1 Install Required Software

```bash
# Install Node.js 18+ (if not already installed)
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install PM2 globally
npm install -g pm2

# Install build tools
sudo apt-get install -y build-essential
```

### 1.2 Create Application Directories

```bash
# Create directories for both applications
sudo mkdir -p /www/wwwroot/crypto.testingtools.site
sudo mkdir -p /www/wwwroot/crypto2.testingtools.site

# Set proper ownership
sudo chown -R www-data:www-data /www/wwwroot/crypto.testingtools.site
sudo chown -R www-data:www-data /www/wwwroot/crypto2.testingtools.site
```

## Step 2: Backend Deployment (crypto2.testingtools.site)

### 2.1 Upload Backend Files

1. Upload the entire `backend` folder to `/www/wwwroot/crypto2.testingtools.site/`
2. Or clone from your repository:

```bash
cd /www/wwwroot/crypto2.testingtools.site
git clone <your-repo-url> .
cd backend
```

### 2.2 Install Dependencies and Build

```bash
cd /www/wwwroot/crypto2.testingtools.site/backend
npm install
npm run build
```

### 2.3 Configure Environment Variables

Create `.env` file in the backend directory:

```bash
cd /www/wwwroot/crypto2.testingtools.site/backend
nano .env
```

Add the following content:

```env
# Environment Configuration
NODE_ENV=production
PORT=5001

# CORS Configuration - Update with your frontend domain
CORS_ORIGINS=https://crypto.testingtools.site,http://localhost:3000

# Database Configuration
DATABASE_URL="postgresql://username:password@localhost:5432/crypto_assistant"

# Exchange API Keys (Optional)
BINANCE_API_KEY=your_binance_api_key
BINANCE_SECRET_KEY=your_binance_secret_key

# External API Keys (Optional)
COINGECKO_API_KEY=your_coingecko_api_key

# Security
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production-make-it-long-and-random
JWT_EXPIRE=24h

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=info

# WebSocket
WS_PORT=5002

# Signal Processing
SIGNAL_UPDATE_INTERVAL=60000
MAX_CANDLE_HISTORY=1000
```

### 2.4 Setup Database

```bash
# Generate Prisma client
npx prisma generate

# Run database migrations
npx prisma db push

# (Optional) Seed database with default settings
npx prisma db seed
```

### 2.5 Create PM2 Configuration

Create `ecosystem.config.js` in the backend directory:

```javascript
module.exports = {
  apps: [{
    name: 'crypto-assistant-backend',
    script: 'dist/index.js',
    cwd: '/www/wwwroot/crypto2.testingtools.site/backend',
    instances: 1,
    exec_mode: 'fork',
    env: {
      NODE_ENV: 'production',
      PORT: 5001
    },
    error_file: '/www/wwwroot/crypto2.testingtools.site/backend/logs/error.log',
    out_file: '/www/wwwroot/crypto2.testingtools.site/backend/logs/out.log',
    log_file: '/www/wwwroot/crypto2.testingtools.site/backend/logs/combined.log',
    time: true,
    max_memory_restart: '1G',
    restart_delay: 4000,
    watch: false,
    ignore_watch: ['node_modules', 'logs']
  }]
};
```

### 2.6 Start Backend with PM2

```bash
cd /www/wwwroot/crypto2.testingtools.site/backend
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

## Step 3: Frontend Deployment (crypto.testingtools.site)

### 3.1 Upload Frontend Files

1. Upload the entire `frontend` folder to `/www/wwwroot/crypto.testingtools.site/`
2. Or clone from your repository:

```bash
cd /www/wwwroot/crypto.testingtools.site
git clone <your-repo-url> .
cd frontend
```

### 3.2 Configure Environment Variables

Create `.env.local` file in the frontend directory:

```bash
cd /www/wwwroot/crypto.testingtools.site/frontend
nano .env.local
```

Add the following content:

```env
# API Configuration
NEXT_PUBLIC_API_URL=https://crypto2.testingtools.site
NEXT_PUBLIC_WS_URL=wss://crypto2.testingtools.site

# Application Configuration
NEXT_PUBLIC_APP_NAME=Crypto Assistant
NEXT_PUBLIC_APP_VERSION=1.0.0
```

### 3.3 Install Dependencies and Build

```bash
cd /www/wwwroot/crypto.testingtools.site/frontend
npm install
npm run build
```

### 3.4 Create PM2 Configuration for Frontend

Create `ecosystem.config.js` in the frontend directory:

```javascript
module.exports = {
  apps: [{
    name: 'crypto-assistant-frontend',
    script: 'node_modules/next/dist/bin/next',
    args: 'start',
    cwd: '/www/wwwroot/crypto.testingtools.site/frontend',
    instances: 1,
    exec_mode: 'fork',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    error_file: '/www/wwwroot/crypto.testingtools.site/frontend/logs/error.log',
    out_file: '/www/wwwroot/crypto.testingtools.site/frontend/logs/out.log',
    log_file: '/www/wwwroot/crypto.testingtools.site/frontend/logs/combined.log',
    time: true,
    max_memory_restart: '1G',
    restart_delay: 4000,
    watch: false
  }]
};
```

### 3.5 Start Frontend with PM2

```bash
cd /www/wwwroot/crypto.testingtools.site/frontend
mkdir -p logs
pm2 start ecosystem.config.js
pm2 save
```

## Step 4: AAPanel Configuration

### 4.1 Create Website for Backend (crypto2.testingtools.site)

1. Go to AAPanel → Website → Add Site
2. Domain: `crypto2.testingtools.site`
3. Document Root: `/www/wwwroot/crypto2.testingtools.site`
4. PHP Version: Pure Static (since we're using Node.js)

### 4.2 Configure Reverse Proxy for Backend

1. Go to the website settings for `crypto2.testingtools.site`
2. Click on "Reverse Proxy"
3. Add new proxy:
   - Target URL: `http://127.0.0.1:5001`
   - Enable: Yes
   - Cache: No (for real-time data)

### 4.3 Create Website for Frontend (crypto.testingtools.site)

1. Go to AAPanel → Website → Add Site
2. Domain: `crypto.testingtools.site`
3. Document Root: `/www/wwwroot/crypto.testingtools.site`
4. PHP Version: Pure Static

### 4.4 Configure Reverse Proxy for Frontend

1. Go to the website settings for `crypto.testingtools.site`
2. Click on "Reverse Proxy"
3. Add new proxy:
   - Target URL: `http://127.0.0.1:3000`
   - Enable: Yes
   - Cache: No

## Step 5: SSL Configuration

### 5.1 Install SSL Certificates

For both domains:
1. Go to AAPanel → Website → Select domain → SSL
2. Choose your preferred method:
   - Let's Encrypt (Free, recommended)
   - Upload custom certificate
   - Use Cloudflare if domains are proxied

### 5.2 Force HTTPS Redirect

1. Enable "Force HTTPS" for both domains
2. This ensures all traffic is encrypted

## Step 6: Firewall and Security

### 6.1 Configure Firewall

Ensure these ports are open:
- 80 (HTTP)
- 443 (HTTPS)
- 22 (SSH)

Block direct access to application ports:
- 3000 (Frontend)
- 5001 (Backend)

### 6.2 Security Headers

Add security headers in AAPanel for both domains:
```
X-Frame-Options: DENY
X-Content-Type-Options: nosniff
X-XSS-Protection: 1; mode=block
Referrer-Policy: strict-origin-when-cross-origin
```

## Step 7: Database Setup

### 7.1 PostgreSQL Installation (Recommended)

```bash
# Install PostgreSQL
sudo apt update
sudo apt install postgresql postgresql-contrib

# Start and enable PostgreSQL
sudo systemctl start postgresql
sudo systemctl enable postgresql

# Create database and user
sudo -u postgres psql
```

In PostgreSQL shell:
```sql
CREATE DATABASE crypto_assistant;
CREATE USER crypto_user WITH ENCRYPTED PASSWORD 'your_secure_password';
GRANT ALL PRIVILEGES ON DATABASE crypto_assistant TO crypto_user;
\q
```

### 7.2 Alternative: SQLite Setup (For Testing)

If you prefer SQLite for testing:

Update your `.env` file:
```env
DATABASE_URL="file:./dev.db"
```

## Step 8: Monitoring and Logs

### 8.1 PM2 Monitoring

```bash
# View all processes
pm2 list

# View logs
pm2 logs crypto-assistant-backend
pm2 logs crypto-assistant-frontend

# Monitor resources
pm2 monit

# Restart applications
pm2 restart crypto-assistant-backend
pm2 restart crypto-assistant-frontend
```

### 8.2 Log Rotation

Create log rotation configuration:

```bash
sudo nano /etc/logrotate.d/crypto-assistant
```

Add:
```
/www/wwwroot/crypto*.testingtools.site/*/logs/*.log {
    daily
    missingok
    rotate 14
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
    postrotate
        pm2 reload all
    endscript
}
```

## Step 9: Backup Strategy

### 9.1 Database Backup

Create backup script:

```bash
sudo nano /usr/local/bin/backup-crypto-db.sh
```

Add:
```bash
#!/bin/bash
BACKUP_DIR="/www/backup/crypto-assistant"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p $BACKUP_DIR

# PostgreSQL backup
pg_dump -h localhost -U crypto_user crypto_assistant > $BACKUP_DIR/crypto_db_$DATE.sql

# Keep only last 7 days of backups
find $BACKUP_DIR -name "crypto_db_*.sql" -mtime +7 -delete

echo "Database backup completed: crypto_db_$DATE.sql"
```

Make executable and add to cron:
```bash
sudo chmod +x /usr/local/bin/backup-crypto-db.sh
sudo crontab -e
```

Add to cron:
```
0 2 * * * /usr/local/bin/backup-crypto-db.sh
```

### 9.2 Application Backup

```bash
# Create application backup script
sudo nano /usr/local/bin/backup-crypto-app.sh
```

Add:
```bash
#!/bin/bash
BACKUP_DIR="/www/backup/crypto-assistant"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p $BACKUP_DIR

# Backup application files (excluding node_modules)
tar -czf $BACKUP_DIR/crypto_app_$DATE.tar.gz \
    --exclude='node_modules' \
    --exclude='logs' \
    --exclude='.git' \
    /www/wwwroot/crypto.testingtools.site \
    /www/wwwroot/crypto2.testingtools.site

# Keep only last 3 days of app backups
find $BACKUP_DIR -name "crypto_app_*.tar.gz" -mtime +3 -delete

echo "Application backup completed: crypto_app_$DATE.tar.gz"
```

## Step 10: Testing and Verification

### 10.1 Backend Testing

```bash
# Test backend API
curl -X GET https://crypto2.testingtools.site/api/health
curl -X GET https://crypto2.testingtools.site/api/coins

# Test WebSocket connection
curl -X GET https://crypto2.testingtools.site/socket.io/
```

### 10.2 Frontend Testing

1. Visit `https://crypto.testingtools.site`
2. Check that the application loads properly
3. Verify real-time data updates
4. Test notification system
5. Check admin panel functionality

### 10.3 Performance Testing

```bash
# Install Apache Bench for testing
sudo apt install apache2-utils

# Test backend performance
ab -n 100 -c 10 https://crypto2.testingtools.site/api/coins

# Test frontend performance
ab -n 50 -c 5 https://crypto.testingtools.site/
```

## Step 11: Troubleshooting

### 11.1 Common Issues

**Backend not starting:**
```bash
# Check PM2 logs
pm2 logs crypto-assistant-backend

# Check if port is in use
sudo netstat -tulpn | grep :5001

# Restart backend
pm2 restart crypto-assistant-backend
```

**Frontend not loading:**
```bash
# Check PM2 logs
pm2 logs crypto-assistant-frontend

# Check build status
cd /www/wwwroot/crypto.testingtools.site/frontend
npm run build

# Restart frontend
pm2 restart crypto-assistant-frontend
```

**Database connection issues:**
```bash
# Test database connection
cd /www/wwwroot/crypto2.testingtools.site/backend
npx prisma db push

# Check PostgreSQL status
sudo systemctl status postgresql
```

**SSL certificate issues:**
- Verify domain DNS points to server
- Check AAPanel SSL configuration
- Ensure firewall allows ports 80 and 443

### 11.2 Performance Optimization

**Backend optimization:**
```bash
# Increase PM2 instances for backend
pm2 scale crypto-assistant-backend 2
```

**Frontend optimization:**
- Enable gzip compression in AAPanel
- Configure browser caching
- Use CDN for static assets

## Step 12: Maintenance

### 12.1 Regular Updates

```bash
# Update application
cd /www/wwwroot/crypto2.testingtools.site/backend
git pull origin main
npm install
npm run build
pm2 restart crypto-assistant-backend

cd /www/wwwroot/crypto.testingtools.site/frontend
git pull origin main
npm install
npm run build
pm2 restart crypto-assistant-frontend
```

### 12.2 Security Updates

```bash
# Update system packages
sudo apt update && sudo apt upgrade

# Update Node.js packages
npm audit fix

# Update PM2
npm update -g pm2
```

## Step 13: Final Checklist

- [ ] Both domains are accessible via HTTPS
- [ ] Backend API endpoints respond correctly
- [ ] Frontend loads and displays data
- [ ] WebSocket connections work
- [ ] Real-time updates are functioning
- [ ] Notification system works
- [ ] Admin panel is accessible
- [ ] Database is properly configured
- [ ] PM2 processes are running
- [ ] SSL certificates are installed
- [ ] Firewall is configured
- [ ] Backup scripts are set up
- [ ] Log rotation is configured
- [ ] Monitoring is in place

## Support

If you encounter any issues during deployment:

1. Check the logs: `pm2 logs`
2. Verify environment variables
3. Test database connectivity
4. Check AAPanel configuration
5. Verify DNS settings
6. Review firewall rules

For additional support, refer to the application documentation or contact the development team.
