module.exports = {
  apps: [{
    name: 'crypto-assistant-frontend',
    script: 'node_modules/next/dist/bin/next',
    args: 'start',
    cwd: '/www/wwwroot/crypto.testingtools.site/frontend',
    instances: 1,
    exec_mode: 'fork',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    env_production: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    error_file: '/www/wwwroot/crypto.testingtools.site/frontend/logs/error.log',
    out_file: '/www/wwwroot/crypto.testingtools.site/frontend/logs/out.log',
    log_file: '/www/wwwroot/crypto.testingtools.site/frontend/logs/combined.log',
    time: true,
    max_memory_restart: '1G',
    restart_delay: 4000,
    watch: false,
    ignore_watch: ['node_modules', 'logs', '.next'],
    min_uptime: '10s',
    max_restarts: 10,
    autorestart: true,
    kill_timeout: 5000,
    listen_timeout: 8000,
    log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
    merge_logs: true,
    // Health check
    health_check_grace_period: 3000
  }]
};
