Your DEPLOYMENT_COMPLETE_GUIDE.md file is for Render.com. Please tell me how I can successfully run this project on AApanel. Please prepare a complete .md file for AApanel. In this .md file, every single small step will be explained, everything will be explained with a full details. No step will be missed, everything will be explained, what to put where, what needs to be done, everything.
Panel Address and Login info
https://blog.testingtools.site:35989/9f51b344
username: yaknsajh
password: Chhw4Y6cXZ
aaPanel Docs
https://www.aapanel.com/docs/Function/Node.html
I am giving you a subdomain that you can use for setup the project: crypto.testingtools.site
If you don't know how to use the AApanel specifically, you can ask me for help. (edited) 



sudo npm install
sudo npm run build
sudo pm2 start dist/index.js --name "backend"
sudo pm2 list 


sudo npm install 
sudo npm run build
pm2 start npm --name frontend -- start
pm2 list

